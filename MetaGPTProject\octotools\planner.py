# filepath: /MetaGPTProject/octotools/planner.py

"""
This module contains the Planner class, which is responsible for managing
planning tasks and workflows within the OctoTools framework. The Planner
class provides methods for defining, executing, and monitoring various
planning activities related to document generation and expert reviews.
"""

import logging
from typing import List, Dict, Any

# Configure logger for the Planner module
logger = logging.getLogger('OctoTools.Planner')

class Planner:
    """
    The Planner class manages planning tasks and workflows within the
    OctoTools framework.

    Attributes:
        tasks (List[Dict[str, Any]]): A list of tasks to be managed by the planner.
    """

    def __init__(self):
        """
        Initializes the Planner with an empty task list.
        """
        self.tasks = []
        logger.info("Planner initialized with an empty task list.")

    def add_task(self, task_name: str, task_details: Dict[str, Any]) -> None:
        """
        Adds a new task to the planner.

        Args:
            task_name (str): The name of the task to be added.
            task_details (Dict[str, Any]): A dictionary containing details about the task.
        """
        task = {
            'name': task_name,
            'details': task_details,
            'status': 'pending'
        }
        self.tasks.append(task)
        logger.info(f"Task '{task_name}' added to the planner.")

    def execute_tasks(self) -> None:
        """
        Executes all tasks in the planner sequentially.
        """
        logger.info("Starting task execution.")
        for task in self.tasks:
            self._execute_task(task)

    def _execute_task(self, task: Dict[str, Any]) -> None:
        """
        Executes a single task.

        Args:
            task (Dict[str, Any]): The task to be executed.
        """
        logger.info(f"Executing task: {task['name']}")
        # Simulate task execution (replace with actual implementation)
        try:
            # Here you would implement the actual logic for the task
            task['status'] = 'completed'
            logger.info(f"Task '{task['name']}' completed successfully.")
        except Exception as e:
            task['status'] = 'failed'
            logger.error(f"Task '{task['name']}' failed with error: {e}")

    def get_task_status(self) -> List[Dict[str, Any]]:
        """
        Returns the status of all tasks.

        Returns:
            List[Dict[str, Any]]: A list of tasks with their current status.
        """
        return [{'name': task['name'], 'status': task['status']} for task in self.tasks]

    def clear_tasks(self) -> None:
        """
        Clears all tasks from the planner.
        """
        self.tasks.clear()
        logger.info("All tasks cleared from the planner.")