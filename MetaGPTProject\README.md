# MetaGPT Project

## Overview
The MetaGPT project is designed to facilitate the generation of Operational Technology (OT) Security documentation through a structured, multi-stage process. This project leverages advanced language models and expert reviews to produce comprehensive and accurate documentation tailored to specific security domains.

## Project Structure
The project is organized into several directories, each serving a distinct purpose:

- **document/**: Contains classes and functions for managing document generation, metadata, processing, and regulatory contexts.
  - `document_generator.py`: Manages the generation of OT Security documentation.
  - `document_metadata.py`: Handles metadata related to documents.
  - `document_processing.py`: Contains functions for processing documents.
  - `regulatory_context.py`: Manages regulatory contexts relevant to the documents.

- **llm/**: Contains classes for interacting with language models.
  - `ollama_client.py`: Interfaces with the Ollama API for text generation.

- **octotools/**: Contains tools for analyzing and enhancing documents.
  - `manager.py`: Manages interactions with OctoTools.
  - `planner.py`: Contains planning-related functions.

- **personas/**: Manages different personas for document reviews.
  - `persona_manager.py`: Handles persona attributes and management.

- **utils/**: Contains utility functions for various tasks.
  - `file_utils.py`: Utility functions for file handling.
  - `logging_utils.py`: Functions for logging configuration.
  - `progress_spinner.py`: Displays a progress spinner in the console.

- **metagpt_cli.py**: The command-line interface for the project, orchestrating the document generation process.

- **requirements.txt**: Lists the dependencies required for the project.

## Installation
To set up the project, clone the repository and install the required dependencies:

```bash
git clone <repository-url>
cd MetaGPTProject
pip install -r requirements.txt
```

## Usage
To generate OT Security documentation, run the command-line interface:

```bash
python metagpt_cli.py --help
```

This will display the available options and usage instructions.

## Contributing
Contributions to the MetaGPT project are welcome! Please submit a pull request or open an issue for any enhancements or bug fixes.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.