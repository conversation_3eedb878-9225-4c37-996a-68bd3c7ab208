import logging as logger 
from typing import Optional
import requests # Moved to top for convention
import json # Moved to top for convention

class OllamaClient:
    """
    OllamaClient interacts with the Ollama API to generate text based on provided prompts.
    This class encapsulates the API calls and handles responses, ensuring a smooth integration
    with the document generation process.
    """

    def __init__(self, api_url: str):
        """
        Initializes the OllamaClient with the specified API URL.

        Args:
            api_url (str): The URL of the Ollama API.
                           It's expected to be the base URL (e.g., http://host:port)
                           or include the /api path (e.g., http://host:port/api).
                           The /generate endpoint will be appended if missing.
        """
        base_api_url = api_url
        if base_api_url.endswith('/api/generate'):
            self.api_url = base_api_url
        elif base_api_url.endswith('/api'):
            self.api_url = base_api_url + '/generate'
        elif base_api_url.endswith('/'):
            self.api_url = base_api_url + 'api/generate'
        else:
            self.api_url = base_api_url + '/api/generate'
        logger.info(f"[OT_Security] OllamaClient initialized. Target API URL: {self.api_url}")


    def generate_text(self, prompt: str, model: str, spinner=None) -> Optional[str]:
        """
        Sends a prompt to the Ollama API and retrieves the generated text.

        Args:
            prompt (str): The prompt to send to the API.
            model (str): The model to use for text generation.
            spinner: Optional spinner for visual feedback during the request.

        Returns:
            Optional[str]: The generated text from the API, or None if an error occurred.
        """
        # Prepare the request payload.
        # stream: False is the default for /api/generate and ensures a single JSON response.
        payload = {
            "prompt": prompt,
            "model": model,
            "stream": False  # Explicitly set stream to False
        }
        
        # Log the call details, but be careful with very long prompts in logs for production
        logger.info(f"[OT_Security] Calling Ollama API. URL: {self.api_url}, Model: {model}")
        # For debugging, you might want to log a snippet of the prompt:
        # logger.debug(f"[OT_Security] Prompt (first 100 chars): {prompt[:100]}...")

        try:
            if spinner:
                spinner.start()

            # Send the request to the Ollama API with a timeout
            response = requests.post(self.api_url, json=payload, timeout=300) # 5-minute timeout

            logger.info(f"[OT_Security] Ollama API response status: {response.status_code}")

            if response.status_code == 200:
                try:
                    response_data = response.json()
                    # The key for the full response text in non-streaming mode for /api/generate is "response"
                    generated_content = response_data.get("response", None)
                    
                    if generated_content is None:
                        logger.warning(f"[OT_Security] Ollama response JSON did not contain 'response' key or it was null. Full response data: {response_data}")
                    # else:
                        # logger.debug(f"[OT_Security] Ollama generated text (first 100 chars): {generated_content[:100] if generated_content else 'None'}")
                    return generated_content
                except json.JSONDecodeError as json_err:
                    logger.error(f"[OT_Security] Ollama response was not valid JSON despite 200 OK. Error: {json_err}")
                    logger.error(f"[OT_Security] Ollama raw response text that failed to parse: {response.text}")
                    return None
            else:
                logger.error(f"[OT_Security] Error from Ollama API: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            # Handle any request exceptions (timeout, connection error, etc.)
            logger.error(f"[OT_Security] Request to Ollama API failed: {e}")
            return None
        finally:
            if spinner:
                spinner.stop()