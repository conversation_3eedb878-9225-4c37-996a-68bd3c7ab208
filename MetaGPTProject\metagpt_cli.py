# filepath: /MetaGPTProject/metagpt_cli.py

import os
import locale
import logging
import asyncio
import aiohttp
import signal
import textstat
import spacy
import io
import json
import traceback
from sentence_transformers import SentenceTransformer, util
import argparse # For parsing command-line arguments

# Import classes from new modules
from personas.persona_manager import PersonaManager
from utils.progress_spinner import ProgressSpinner
from utils.file_utils import create_folder_structure, list_files, select_file, check_file_access, load_security_domains, select_security_domain
from utils.logging_utils import setup_logging, audit_logger
from document.document_metadata import DocumentMetadata
from document.document_processing import save_document_in_format, parse_controls_from_content
from document.regulatory_context import RegulatoryContext
from octotools.manager import OctoToolsManager
from octotools.planner import Planner

# Import OllamaClient to apply the patch
from llm.ollama_client import OllamaClient # Ensure this import exists

# Import your DocumentOrchestrator
from document.document_orchestrator import DocumentOrchestrator
# Import other necessary utilities like list_files, select_file if they were used for interaction
from utils.file_utils import list_files, select_file 

# Set the locale to English
os.environ["LC_ALL"] = "en_US.UTF-8"
os.environ["LANG"] = "en_US.UTF-8"
locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')

# --- MODEL CONSISTENCY PATCH ---
# Store the original method
_original_ollama_generate_text = OllamaClient.generate_text

def patched_ollama_generate_text(self, *args, **kwargs):
    """
    Patched version of OllamaClient.generate_text to enforce a specific model.
    Accepts any number of arguments to prevent signature mismatch errors.
    """
    forced_model = "cabreza_docgen"
    current_logger = logging.getLogger('OT_Security')

    # Log all received arguments for debugging
    current_logger.error(f"[MODEL_PATCH] patched_ollama_generate_text called with args={args}, kwargs={kwargs}")

    # Extract prompt, model, spinner from args/kwargs as best as possible
    prompt = None
    spinner = None

    # Try to extract prompt and spinner from positional or keyword arguments
    if len(args) > 0:
        prompt = args[0]
    if len(args) > 2:
        spinner = args[2]
    if 'prompt' in kwargs:
        prompt = kwargs['prompt']
    if 'spinner' in kwargs:
        spinner = kwargs['spinner']

    # Always use forced_model
    try:
        result = _original_ollama_generate_text(self, prompt=prompt, model=forced_model, spinner=spinner)
        current_logger.info(
            f"[MODEL_PATCH] Original call returned successfully. "
            f"Result type: {type(result)}, "
            f"Result length (if str): {len(result) if isinstance(result, str) else 'N/A'}"
        )
        return result
    except Exception as e:
        current_logger.error(f"[MODEL_PATCH] EXCEPTION DURING ORIGINAL CALL: {e}", exc_info=True)
        raise

# Apply the patch
OllamaClient.generate_text = patched_ollama_generate_text
# --- SANITY CHECK ---
print("DEBUG: OllamaClient.generate_text has been patched in metagpt_cli.py.", flush=True)
logging.getLogger('OT_Security').info("OT_SECURITY_DEBUG: OllamaClient.generate_text has been patched in metagpt_cli.py.")
# --- END SANITY CHECK ---
# --- END MODEL CONSISTENCY PATCH ---

def setup_logging():
    """Initialize logging configuration."""
    logger = logging.getLogger('OT_Security')
    logger.setLevel(logging.INFO)
    
    # Clear any existing handlers
    logger.handlers = []
    
    # Create console handler with a higher log level
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    
    # Create formatter and add it to the handler
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    ch.setFormatter(formatter)
    
    # Add the handler to the logger
    logger.addHandler(ch)

async def main():
    """Main function to execute the CLI."""
    setup_logging()
    logger = logging.getLogger('OT_Security')

    # --- Argument Parsing ---
    parser = argparse.ArgumentParser(description="MetaGPT Document Generation CLI")
    parser.add_argument("--templates-dir", default="/app/templates", help="Directory for document templates")
    parser.add_argument("--prompts-dir", default="/app/prompts", help="Directory for prompt files")
    parser.add_argument("--surveys-dir", default="/app/surveys", help="Directory for customer survey files")
    parser.add_argument("--destination", default="/app/output", help="Directory to save generated documents")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode")
    # Add an argument for the security domains file if you want it to be configurable
    parser.add_argument("--security-domains-file", default="/app/SecurityDomain.txt", help="Path to the security domains file")

    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)
        logger.debug("==== DEBUG: Command Line Arguments ====")
        for arg, value in vars(args).items():
            logger.debug(f"{arg}: {value}")
        logger.debug("======================================")
        # You might also want to print environment variables if needed for debugging
        logger.debug("==== DEBUG: Environment Variables ====")
        ollama_api_url_env = os.getenv("OLLAMA_API_URL", "http://V1_ollama:11434/api") # Example
        logger.debug(f"OLLAMA_API_URL from env: {ollama_api_url_env}")
        logger.debug("======================================")

    # --- Load Security Domains (already implemented) ---
    security_domains_file_path = args.security_domains_file
    try:
        security_domains = load_security_domains(file_path=security_domains_file_path)
        if security_domains:
            logger.info(f"Loaded security domains: {security_domains}")
        else:
            logger.warning(f"No security domains loaded from {security_domains_file_path}. Proceeding without predefined domains.")
            security_domains = []
    except FileNotFoundError:
        logger.error(f"Security domains file not found at {security_domains_file_path}.")
        security_domains = []
    except Exception as e:
        logger.error(f"Error loading security domains from {security_domains_file_path}: {e}", exc_info=True)
        security_domains = []

    logger.info("Starting main CLI operations...")

    # --- Initialize Core Components (Example) ---
    # This is where you'd initialize your main classes
    # The OLLAMA_API_URL should ideally come from config or env
    ollama_api_url = os.getenv("OLLAMA_API_URL", "http://V1_ollama:11434/api")
    logger.info(f"Initializing Ollama client with: {ollama_api_url}")
    
    # Ensure OllamaClient is imported if not already at the top
    # from llm.ollama_client import OllamaClient 
    ollama_client = OllamaClient(api_url=ollama_api_url)
    logger.info(f"[OT_Security] Initialized Ollama client with base URL: {ollama_client.api_url.replace('/api', '') if ollama_client.api_url else 'N/A'}")

    # Initialize DocumentOrchestrator
    # You might need to pass a configuration dictionary to DocumentOrchestrator
    # For now, let's assume a simple initialization.
    # Refer to your DocumentOrchestrator.__init__ for required arguments.
    try:
        # Define a meaningful configuration for the orchestrator
        orchestrator_config = {
            'workflow': [
                'terminology', 
                'citation', 
                'formatting', 
                'acronyms', 
                'structure', 
                'json_cleanup', 
                'content_fixer',
                'compliance', # Ensure compliance is in your workflow if needed
                'tables'      # If you use the table generator agent
            ],
            'agents': {
                # Add any agent-specific configurations here if needed
                # 'terminology': {'model': 'your_preferred_model_for_terminology'},
                # 'compliance': {'regulatory_framework': 'NERC-CIP'} # Example
            }
        }
        orchestrator = DocumentOrchestrator(
            config=orchestrator_config,
            ollama_client=ollama_client
        )
        logger.info(f"[OT_Security] Initialized DocumentOrchestrator with process ID: {orchestrator.process_id if hasattr(orchestrator, 'process_id') else 'N/A'}")
    except Exception as e:
        logger.error(f"Failed to initialize DocumentOrchestrator: {e}", exc_info=True)
        return # Can't proceed without the orchestrator

    # --- Step 1: Validating environment (Example) ---
    logger.info("Step 1: Validating environment...")
    try:
        # Assuming your orchestrator or ollama_client has a health check method
        # For example, if OllamaClient has a check_health method:
        # health_ok = await ollama_client.check_health() # if it's async
        # Or if orchestrator has it:
        # health_ok = await orchestrator.check_ollama_health() # if it's async
        
        # Placeholder: Let's assume a simple synchronous check for now if available
        # or simulate it. The actual health check logic is in your classes.
        # This part needs to align with how your health check is implemented.
        # For now, we'll just log that we'd do it.
        logger.info("[OT_Security] Ollama API health check would be performed here.")
        # A true health check would involve an actual API call.
        # Example:
        # if hasattr(ollama_client, 'is_healthy') and callable(getattr(ollama_client, 'is_healthy')):
        #     if ollama_client.is_healthy(): # Assuming a synchronous method for simplicity here
        #         logger.info("✓ Ollama API health check passed.")
        #     else:
        #         logger.error("Ollama API health check failed. Exiting.")
        #         return
        # else:
        #     logger.warning("Ollama client does not have a direct health check method. Assuming healthy.")
        pass # Replace with actual health check logic

    except Exception as e:
        logger.error(f"Error during environment validation: {e}", exc_info=True)
        return

    # --- Main Application Workflow (Example - adapt from your orchestrator) ---
    try:
        # This is where you'd call the main processing method of your orchestrator.
        # For example, if your orchestrator has a method like `run_full_pipeline`
        # or separate methods for each step.

        # Example: Listing surveys and getting selection (adapt from your previous logic)
        logger.info("\nAvailable customer surveys:")
        survey_files = list_files(args.surveys_dir, ".txt") # Ensure list_files is imported
        if not survey_files:
            logger.error(f"No survey files found in {args.surveys_dir}. Exiting.")
            return

        for i, survey_file in enumerate(survey_files):
            logger.info(f"{i+1}. {survey_file}")
        
        selected_survey_file = None
        while selected_survey_file is None:
            try:
                selection = input(f"Please select a survey (1-{len(survey_files)}): ")
                selected_survey_index = int(selection) - 1
                if 0 <= selected_survey_index < len(survey_files):
                    selected_survey_file = survey_files[selected_survey_index]
                    logger.info(f"You selected: {selected_survey_file}")
                else:
                    logger.warning("Invalid selection. Please try again.")
            except ValueError:
                logger.warning("Invalid input. Please enter a number.")
            except EOFError:
                logger.error("No input received. Cannot proceed in non-interactive mode without defaults. Exiting.")
                return
        
        # Construct full path for the selected survey
        full_survey_path = os.path.join(args.surveys_dir, selected_survey_file)

        # You would then pass this, and other parameters like selected template,
        # security domain, etc., to your orchestrator's main processing method.

        # Example metadata to pass to orchestrator methods
        # This would be built up from user selections, survey data, etc.
        document_metadata = DocumentMetadata(
             customer_name="Customer From Survey", # Extract from survey
             security_domain="Selected Domain",    # Get from user or survey
             document_title=f"OT Security Standard for Customer",
             # ... other metadata fields
        ).to_dict()
        
        # Placeholder for actual document generation call
        # This needs to match how your DocumentOrchestrator is designed to be called.
        # For instance, if it has a method like `generate_document_from_survey`:
        # final_document_path = await orchestrator.generate_document_from_survey(
        #     survey_file_path=full_survey_path,
        #     template_name="your_template.docx", # This would also be selected by user
        #     output_dir=args.destination,
        #     metadata_override=document_metadata
        # )
        # if final_document_path:
        #    logger.info(f"Document generation complete. Final document at: {final_document_path}")
        # else:
        #    logger.error("Document generation failed.")
        
        logger.info("Placeholder: Main document generation workflow would run here using the orchestrator.")
        logger.info(f"Selected survey: {full_survey_path}")
        logger.info(f"Templates directory: {args.templates_dir}")
        logger.info(f"Output directory: {args.destination}")

        logger.info(f"Processing survey: {full_survey_path}")
        logger.info(f"Templates directory: {args.templates_dir}")
        logger.info(f"Output directory: {args.destination}")

        # 1. Load and parse the selected survey to get initial content/context
        #    This part needs to be implemented based on how your surveys are structured
        #    and how they feed into the initial document.
        #    For example:
        #    customer_context = load_survey_data(full_survey_path) # You'll need this function

        # 2. Select a document template
        #    This would involve listing templates and getting user input, similar to surveys.
        #    selected_template_path = os.path.join(args.templates_dir, "your_chosen_template.docx") # Example

        # 3. Create initial document_json and metadata
        #    This is a CRITICAL step. How is the first version of the document created?
        #    Is there a DocumentGenerator.generate_initial_document() method?
        #    Or does the orchestrator handle this from raw inputs?

        #    Placeholder for initial document JSON and metadata:
        #    You MUST replace this with actual logic to create/load the initial document.
        initial_document_json = {
            "title": "Initial Document Title from Survey/Template",
            "sections": [
                {"heading": "Introduction", "content": "Content from survey: " + "placeholder_survey_intro"},
                {"heading": "Scope", "content": "Content from survey: " + "placeholder_survey_scope"}
            ]
            # ... more sections based on survey/template
        }
        
        # Create DocumentMetadata instance
        # You'll need to populate this based on survey, user input, etc.
        # from document.document_metadata import DocumentMetadata # Ensure imported
        doc_metadata_obj = DocumentMetadata(
             customer_name="Customer From Survey", # Extract from survey
             security_domain=security_domains[0] if security_domains else "Default Domain", # Example, get from user
             document_title=initial_document_json.get("title", "OT Security Standard"),
             version="1.0",
             status="Draft",
             # ... other metadata fields
        )
        document_metadata_dict = doc_metadata_obj.to_dict() # Pass the dict to orchestrator

        logger.info(f"Attempting to process document with metadata: {document_metadata_dict}")

        # 4. Call the orchestrator to process the document
        #    Assuming process_document is the main entry point for an existing doc structure
        #    If your orchestrator has a method to generate from scratch, use that.
        #    This method is synchronous in your current orchestrator.
        processed_document_json = orchestrator.process_document(
            document_json=initial_document_json,
            metadata=document_metadata_dict
        )

        if processed_document_json:
            logger.info("Document processing by orchestrator complete.")
            # 5. Save the final document
            #    You'll need a function to save the processed_document_json to a file.
            #    from document.document_processing import save_document_in_format # Ensure imported
            
            # Example: Saving as JSON first
            final_doc_filename_base = f"{doc_metadata_obj.customer_name.replace(' ', '_')}_{doc_metadata_obj.security_domain.replace(' ', '_')}_Standard"
            output_file_path = save_document_in_format(
                content=json.dumps(processed_document_json, indent=4), # Save JSON string
                base_path=args.destination,
                base_filename=final_doc_filename_base,
                output_format="json" # Or "docx" if you have a JSON to DOCX converter
            )
            if output_file_path:
                logger.info(f"Final document saved to: {output_file_path}")
            else:
                logger.error("Failed to save the final document.")
        else:
            logger.error("Document processing by orchestrator failed or returned no result.")

    except Exception as e:
        logger.error(f"An error occurred during the main CLI operations: {e}", exc_info=True)

    logger.info("CLI execution finished.")

if __name__ == "__main__":
    signal.signal(signal.SIGINT, lambda s, f: print("\nInterrupted by user!"))
    signal.signal(signal.SIGTERM, lambda s, f: print("\nTerminated!"))
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logging.getLogger('OT_Security').info("CLI process interrupted by user (KeyboardInterrupt).")
    except Exception as e:
        logging.getLogger('OT_Security').critical(f"Unhandled exception in __main__: {e}", exc_info=True)