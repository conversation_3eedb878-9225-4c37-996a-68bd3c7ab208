# filepath: /MetaGPTProject/utils/file_utils.py

"""
Utility functions for file handling, including reading and writing files.
"""

import os
from typing import List, Optional

def create_folder_structure(base_path: str, subfolders: List[str]) -> None:
    """
    Create a folder structure at the specified base path.

    Args:
        base_path (str): The base directory where the folder structure will be created.
        subfolders (List[str]): A list of subfolder names to create within the base path.
    """
    for folder in subfolders:
        path = os.path.join(base_path, folder)
        try:
            os.makedirs(path, exist_ok=True)  # Create the directory if it doesn't exist
            print(f"Created folder: {path}")
        except Exception as e:
            print(f"Error creating folder {path}: {e}")

def list_files(directory: str) -> List[str]:
    """
    List all files in the specified directory.

    Args:
        directory (str): The directory to list files from.

    Returns:
        List[str]: A list of file names in the directory.
    """
    try:
        files = os.listdir(directory)
        return [f for f in files if os.path.isfile(os.path.join(directory, f))]
    except Exception as e:
        print(f"Error listing files in directory {directory}: {e}")
        return []

def select_file(directory: str, file_name: str) -> Optional[str]:
    """
    Select a specific file from the directory.

    Args:
        directory (str): The directory to search for the file.
        file_name (str): The name of the file to select.

    Returns:
        Optional[str]: The full path of the selected file, or None if not found.
    """
    file_path = os.path.join(directory, file_name)
    if os.path.isfile(file_path):
        return file_path
    else:
        print(f"File {file_name} not found in directory {directory}.")
        return None

def check_file_access(file_path: str) -> bool:
    """
    Check if the specified file can be accessed.

    Args:
        file_path (str): The path to the file to check.

    Returns:
        bool: True if the file can be accessed, False otherwise.
    """
    return os.access(file_path, os.R_OK)

def load_security_domains(file_path: str) -> List[str]:
    """
    Load security domains from a specified file.

    Args:
        file_path (str): The path to the file containing security domains.

    Returns:
        List[str]: A list of security domains.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            domains = f.read().splitlines()
        return domains
    except Exception as e:
        print(f"Error loading security domains from {file_path}: {e}")
        return []

def select_security_domain(domains: List[str]) -> Optional[str]:
    """
    Select a security domain from the provided list.

    Args:
        domains (List[str]): A list of security domains to choose from.

    Returns:
        Optional[str]: The selected security domain, or None if no valid selection was made.
    """
    print("Select a security domain:")
    for idx, domain in enumerate(domains, 1):
        print(f"{idx}. {domain}")
    
    try:
        choice = int(input("Enter the number for your choice: ")) - 1
        if 0 <= choice < len(domains):
            return domains[choice]
        else:
            print("Invalid choice. Please try again.")
            return None
    except ValueError:
        print("Invalid input. Please enter a number.")
        return None