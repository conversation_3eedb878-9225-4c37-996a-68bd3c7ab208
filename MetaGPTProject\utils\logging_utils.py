# filepath: /MetaGPTProject/MetaGPTProject/utils/logging_utils.py

import logging
import os

def setup_logging(log_file: str = "application.log", log_level: int = logging.INFO) -> None:
    """
    Set up logging configuration for the application.

    Args:
        log_file (str): The name of the log file where logs will be saved.
        log_level (int): The logging level (e.g., logging.DEBUG, logging.INFO).
    """
    # Create a directory for logs if it doesn't exist
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    # Configure the logging settings
    logging.basicConfig(
        filename=log_file,
        filemode='a',  # Append mode
        format='%(asctime)s - %(levelname)s - %(message)s',
        level=log_level
    )

    # Create a console handler for logging to the console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)

    # Create a formatter for the console handler
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)

    # Add the console handler to the root logger
    logging.getLogger().addHandler(console_handler)

    logging.info("Logging is set up. Log file: %s", log_file)

def audit_logger() -> logging.Logger:
    """
    Create and return an audit logger.

    Returns:
        logging.Logger: A logger configured for auditing purposes.
    """
    audit_log = "audit.log"
    logger = logging.getLogger('audit')
    logger.setLevel(logging.INFO)

    # Create a file handler for the audit log
    file_handler = logging.FileHandler(audit_log)
    file_handler.setLevel(logging.INFO)

    # Create a formatter for the audit log
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)

    # Add the file handler to the audit logger
    logger.addHandler(file_handler)

    return logger