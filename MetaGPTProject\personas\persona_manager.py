# filepath: /MetaGPTProject/personas/persona_manager.py

"""
PersonaManager class for managing different personas and their attributes
for document reviews in the OT Security documentation generation process.
"""

import json
import logging
from typing import Dict, List, Any

# Configure logger
logger = logging.getLogger('OT_Security')

class PersonaManager:
    """
    Manages personas used in the document review process.
    
    Attributes:
        personas (List[Dict[str, Any]]): A list of persona dictionaries containing
                                           attributes such as name, role, expertise, etc.
    """
    
    def __init__(self, persona_file: str):
        """
        Initializes the PersonaManager with a list of personas loaded from a JSON file.
        
        Args:
            persona_file (str): Path to the JSON file containing persona definitions.
        """
        self.personas = self.load_personas(persona_file)
    
    def load_personas(self, persona_file: str) -> List[Dict[str, Any]]:
        """
        Loads personas from a JSON file.
        
        Args:
            persona_file (str): Path to the JSON file containing persona definitions.
        
        Returns:
            List[Dict[str, Any]]: A list of personas loaded from the file.
        """
        try:
            with open(persona_file, 'r', encoding='utf-8') as f:
                personas = json.load(f)
                logger.info(f"Loaded {len(personas)} personas from {persona_file}")
                return personas
        except Exception as e:
            logger.error(f"Error loading personas from {persona_file}: {e}")
            return []
    
    def get_persona(self, name: str) -> Dict[str, Any]:
        """
        Retrieves a persona by name.
        
        Args:
            name (str): The name of the persona to retrieve.
        
        Returns:
            Dict[str, Any]: The persona dictionary if found, otherwise None.
        """
        for persona in self.personas:
            if persona.get('name') == name:
                logger.debug(f"Retrieved persona: {name}")
                return persona
        logger.warning(f"Persona not found: {name}")
        return None
    
    def get_all_personas(self) -> List[Dict[str, Any]]:
        """
        Retrieves all personas.
        
        Returns:
            List[Dict[str, Any]]: A list of all personas.
        """
        logger.debug("Retrieving all personas")
        return self.personas
    
    def add_persona(self, persona: Dict[str, Any]) -> None:
        """
        Adds a new persona to the list.
        
        Args:
            persona (Dict[str, Any]): The persona dictionary to add.
        """
        self.personas.append(persona)
        logger.info(f"Added new persona: {persona.get('name')}")
    
    def save_personas(self, persona_file: str) -> None:
        """
        Saves the current list of personas to a JSON file.
        
        Args:
            persona_file (str): Path to the JSON file to save personas.
        """
        try:
            with open(persona_file, 'w', encoding='utf-8') as f:
                json.dump(self.personas, f, indent=2)
                logger.info(f"Saved {len(self.personas)} personas to {persona_file}")
        except Exception as e:
            logger.error(f"Error saving personas to {persona_file}: {e}")