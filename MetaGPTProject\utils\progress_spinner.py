class ProgressSpinner:
    """
    A class to display a progress spinner in the console.
    This is useful for indicating that a process is ongoing, especially for long-running tasks.
    """

    def __init__(self, message: str = "Loading"):
        """
        Initialize the ProgressSpinner with a message.

        Args:
            message (str): The message to display alongside the spinner.
        """
        self.message = message
        self.spinner_active = False
        self.spinner_symbols = ['|', '/', '-', '\\']  # Spinner symbols to rotate through
        self.current_symbol_index = 0

    def start(self):
        """
        Start the spinner.
        This method will begin the spinner animation in a separate thread.
        """
        if not self.spinner_active:
            self.spinner_active = True
            self._animate_spinner()

    def stop(self):
        """
        Stop the spinner.
        This method will stop the spinner animation.
        """
        self.spinner_active = False
        print("\r", end="")  # Clear the spinner line

    def _animate_spinner(self):
        """
        Animate the spinner in the console.
        This method will rotate through the spinner symbols while the spinner is active.
        """
        import sys
        import time

        while self.spinner_active:
            # Print the current spinner symbol
            sys.stdout.write(f"\r{self.message} {self.spinner_symbols[self.current_symbol_index]}")
            sys.stdout.flush()
            self.current_symbol_index = (self.current_symbol_index + 1) % len(self.spinner_symbols)
            time.sleep(0.1)  # Adjust the speed of the spinner here

        # Clear the spinner line when stopped
        sys.stdout.write("\r")  # Clear the spinner line
        sys.stdout.flush()